# Enhanced JSON Content Replacement System

## Overview

The page builder now includes a powerful **JSON Content Replacement System** that allows users to:
- Edit JSON content in real-time through a right-side panel
- See instant updates in the preview as they type
- Mix JSON content with slug-based patterns
- Handle nested object access and arrays

## Key Features

### 1. JSON Content Editor Panel
- **Access**: Click "JSON Content" button in demo mode toolbar
- **Real-time editing**: Changes reflect immediately in preview
- **JSON validation**: Invalid JSON doesn't break the system
- **Syntax examples**: Built-in help with pattern examples

### 2. Content Replacement Patterns

#### Simple JSON Replacement
```html
<h1>${title}</h1>
<p>${description}</p>
```
With JSON:
```json
{
  "title": "Welcome to Dream Builder",
  "description": "Build amazing websites"
}
```
Result:
```html
<h1>Welcome to Dream Builder</h1>
<p>Build amazing websites</p>
```

#### Nested Object Access
```html
<p>${firstName} ${lastName}</p>
<span>${address.city}, ${address.zipCode}</span>
```
With JSON:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "address": {
    "city": "Anytown",
    "zipCode": "12345"
  }
}
```
Result:
```html
<p>John Doe</p>
<span>Anytown, 12345</span>
```

#### Array Handling
```html
<p>Courses: ${courses}</p>
```
With JSON:
```json
{
  "courses": ["History", "Math", "Science"]
}
```
Result:
```html
<p>Courses: History, Math, Science</p>
```

#### Mixed JSON + Slug Patterns
```html
<h1>Hello ${firstName}, welcome to ${slug}</h1>
<p>Location: ${constValue?.city[${slug}]?.location1}</p>
<p>Contact: ${phone} | ${email}</p>
```
With slug "Ahmedabad" and JSON data:
```json
{
  "firstName": "John",
  "phone": "+****************",
  "email": "<EMAIL>"
}
```
Result:
```html
<h1>Hello John, welcome to Ahmedabad</h1>
<p>Location: Gujarat's Commercial Hub</p>
<p>Contact: +**************** | <EMAIL></p>
```

## Demo Components

### 1. JSON Content Showcase
Demonstrates all JSON replacement patterns:
- Simple key replacement
- Nested object access
- Array display
- Mixed content types

### 2. Mixed Content Component
Shows combination of:
- JSON content (${firstName}, ${phone})
- Slug patterns (${slug})
- constValue access (${constValue?.city[${slug}]?.location1})

## Default JSON Content

```json
{
  "title": "Welcome to Dream Builder",
  "subtitle": "Build Amazing Websites",
  "description": "Create stunning websites with our drag-and-drop builder",
  "button_text": "Get Started",
  "firstName": "John",
  "lastName": "Doe",
  "age": 30,
  "isStudent": false,
  "courses": ["History", "Math"],
  "address": {
    "street": "123 Main St",
    "city": "Anytown",
    "zipCode": "12345"
  },
  "contact": null,
  "company_name": "Dream Builder Inc",
  "phone": "+****************",
  "email": "<EMAIL>"
}
```

## How to Use

### Step 1: Enable Demo Mode
1. Navigate to `/demo` or open page builder
2. Toggle "Demo Mode" switch in toolbar

### Step 2: Open JSON Editor
1. Click "JSON Content" button in toolbar
2. Right-side drawer opens with JSON editor

### Step 3: Edit Content
1. Modify JSON values in the editor
2. See real-time updates in preview
3. Try different slug values from dropdown

### Step 4: Test Patterns
1. Add components with different placeholder patterns
2. Use ${key} for simple replacement
3. Use ${object.property} for nested access
4. Mix with ${slug} and constValue patterns

## Technical Implementation

### Content Replacement Function
```javascript
replaceContentWithJson(content, jsonContent, slug, constants, templates)
```

### Processing Order
1. **Slug-based patterns**: ${constValue?.city[${slug}]?.location1}
2. **Template patterns**: ${city_hero_title}
3. **JSON content**: ${title}, ${address.city}
4. **Fallback**: Keep original if not found

### Error Handling
- Invalid JSON doesn't break preview
- Missing keys show original placeholder
- Nested access safely handles undefined values
- Arrays automatically join with commas

## Benefits

1. **Real-time feedback**: See changes instantly
2. **Flexible content**: Mix static and dynamic patterns
3. **User-friendly**: No coding required
4. **Robust**: Handles errors gracefully
5. **Extensible**: Easy to add new patterns

This system provides a complete solution for dynamic content management in the page builder, allowing users to create rich, data-driven pages with ease.
