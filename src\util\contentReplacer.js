const evaluateNestedPath = (obj, path) => {
    if (!path) return obj;
    const properties = path.split('?.');
    let result = obj;

    for (const prop of properties) {
        if (result == null) return undefined;
        // Handle array/object access with dynamic values
        const match = prop.match(/\[(.*?)\]/);
        if (match) {
            const key = match[1].replace(/\${|}/g, ''); // Remove ${} if present
            result = result[key];
        } else {
            result = result[prop];
        }
    }
    return result;
};

const replaceContent = (content, data, constants) => {
    // Replace nested object access patterns first
    content = content.replace(/\${([^}]+)}/g, (match, path) => {
        // If the path contains object access
        if (path.includes('?.') || path.includes('[')) {
            return evaluateNestedPath(constants, path) || match;
        }

        // For simple replacements, use data object
        return data[path] || match;
    });

    return content;
};

const constValue = {
    city: {
        Ahmedabad: {
            location1: "Sample Location Value"
        },
        Noida: "NCR"
    }
};

const defaultContent = {
    city_hero_title: "Hello, ${slug}, ${constValue?.city[${slug}]?.location1}",
    service_hero_title: "We are providing ${slug}"
};

export { replaceContent, constValue, defaultContent };
