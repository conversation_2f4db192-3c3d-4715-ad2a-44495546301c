import React from "react";
import { Navigate } from "react-router-dom";
import Login from "../components/Auth/Login";
import Dashboard from "../components/Dashboard/Dashboard";
import Layout from "../components/Layout/Layout";
import PageBuilder from "../components/Pages/PageBuilder";
import TemplateManager from "../components/Templates/TemplateManager";
import UserManager from "../components/Users/<USER>";
import WebsiteManager from "../components/Websites/WebsiteManager";
import { AuthProvider, useAuth } from "../contexts/AuthContext";
import ComponentManager from "../components/Components/ComponentManager";
import CategoryManager from "../components/Categories/CategoryManager";
import ActivityLogs from "../components/ActivityLogs/ActivityLogs";
import { Spin } from "antd";

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="tw-flex tw-items-center tw-justify-center tw-min-h-screen">
        <Spin />
      </div>
    );
  }

  return user ? <>{children}</> : <Navigate to="/login" replace />;
};

export const ROUTES = {
  dashboard: "/",
};

export const appRoot = "/";

const ALL_ROUTES = (appProps) => [
  // Login route (separate from protected routes)
  {
    path: "/login",
    element: <Login />,
  },

  // Protected routes with Layout
  {
    path: "/",
    element: (
      <ProtectedRoute>
        <Layout {...appProps} />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Dashboard {...appProps} />,
      },
      {
        path: "pages",
        element: <PageBuilder {...appProps} />,
      },
      {
        path: "categories",
        element: <CategoryManager {...appProps} />,
      },
      {
        path: "components",
        element: <ComponentManager {...appProps} />,
      },
      {
        path: "templates",
        element: <TemplateManager {...appProps} />,
      },
      {
        path: "websites",
        element: <WebsiteManager {...appProps} />,
      },
      {
        path: "activity-logs",
        element: <ActivityLogs {...appProps} />,
      },
      {
        path: "users",
        element: <UserManager {...appProps} />,
      },
    ],
  },

  // Fallback route
  {
    path: "*",
    element: <Navigate to="/login" replace />,
  },
];

export default ALL_ROUTES;
