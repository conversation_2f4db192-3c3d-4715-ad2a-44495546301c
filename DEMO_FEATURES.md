# Page Builder Demo Features - Enhanced JSON Content System

## Overview

This implementation provides a comprehensive demo for the page tab functionality with drag & drop components, preview capabilities, and **real-time JSON content replacement system** that allows users to edit content data and see instant updates in the preview.

## Features Implemented

### 1. Demo Mode Toggle

- **Location**: Top toolbar in DragDropBuilder
- **Functionality**: Switch between normal mode and demo mode
- **Auto-activation**: Demo mode is automatically enabled when no page is provided (pure demo scenario)

### 2. Drag & Drop Components

- **Line-by-line placement**: Components can be dragged and dropped between existing components
- **Visual feedback**: Drop zones appear with blue highlighting when dragging
- **Demo components**: Pre-built components specifically for demonstration

### 3. Dynamic Content Replacement System

#### Static Content Replacement

```html
<h1>${title}</h1>
```

Becomes:

```html
<h1>Welcome to Dream Builder</h1>
```

#### Slug-based Dynamic Content

```html
<h1>Hello, ${slug}, ${constValue?.city[${slug}]?.location1}</h1>
```

With slug "Ahmedabad" becomes:

```html
<h1>Hello, Ahmedabad, Gujarat's Commercial Hub</h1>
```

#### Service-based Content

```html
<p>We are providing ${service_hero_title}</p>
<span>${service_price}</span>
```

With slug "web-development" becomes:

```html
<p>We are providing web-development services</p>
<span>Starting at $2,999</span>
```

### 4. Demo Components Added

#### Hero Section (`demo-hero-1`)

- Basic hero with title, description, and button
- Uses static placeholders: `${title}`, `${description}`, `${button_text}`

#### City Hero Section (`demo-city-hero`)

- Location-specific hero section
- Uses dynamic placeholders: `${city_hero_title}`, `${city_hero_subtitle}`, `${city_population}`, `${city_specialty}`

#### Service Card (`demo-service-card`)

- Service showcase component
- Uses service placeholders: `${service_hero_title}`, `${service_description}`, `${service_price}`, `${service_duration}`

#### Contact Section (`demo-contact`)

- Contact form with location-based title
- Uses: `${contact_title}`, `${testimonial_location}`

### 5. Slug Options Available

- **Cities**: Ahmedabad, Noida, Mumbai, Delhi
- **Services**: web-development, mobile-app, digital-marketing

### 6. Content Data Structure

#### City Data

```javascript
constValue = {
  city: {
    Ahmedabad: {
      location1: "Gujarat's Commercial Hub",
      location2: "Sabarmati Riverfront",
      population: "8.2 million",
      specialty: "Textile Industry",
    },
    // ... other cities
  },
};
```

#### Service Data

```javascript
constValue = {
  service: {
    "web-development": {
      title: "Web Development",
      description: "Custom websites and web applications",
      price: "$2,999",
      duration: "4-6 weeks",
    },
    // ... other services
  },
};
```

### 7. Content Templates

```javascript
contentTemplates = {
  // Static templates
  title: "Welcome to Dream Builder",
  subtitle: "Build Amazing Websites",

  // Dynamic templates with slug support
  city_hero_title: "Hello, ${slug}, ${constValue?.city[${slug}]?.location1}",
  service_hero_title: "We are providing ${slug} services",
  service_price: "Starting at ${constValue?.service[${slug}]?.price}",
  // ... more templates
};
```

### 8. Preview Functionality

- **Responsive preview**: Desktop, tablet, mobile views
- **Real-time updates**: Content updates immediately when slug changes
- **iframe rendering**: Complete HTML preview with CSS and JavaScript

### 9. Demo Page Route

- **URL**: `/demo`
- **Navigation**: Added to sidebar with Play icon
- **Landing page**: Explains demo features before starting
- **Auto-demo mode**: Automatically enables demo mode when accessed

## Usage Instructions

### Accessing Demo Mode

1. Navigate to `/demo` from the sidebar
2. Click "Start Demo" button
3. Demo mode will be automatically enabled

### Using Demo Features

1. **Toggle Demo Mode**: Use the switch in the top toolbar
2. **Select Slug**: Choose from dropdown (Ahmedabad, Noida, etc.)
3. **Drag Components**: Drag demo components from sidebar to canvas
4. **Preview Changes**: See real-time updates in the preview area
5. **Test Responsive**: Switch between desktop/tablet/mobile views

### Content Pattern Examples

- `${title}` → Static replacement
- `${city_hero_title}` → "Hello, Ahmedabad, Gujarat's Commercial Hub"
- `${service_price}` → "Starting at $2,999"
- `${constValue?.city[${slug}]?.location1}` → Direct data access

## Technical Implementation

### Files Modified/Created

1. `src/util/demoContent.js` - Enhanced with dummy components and slug system
2. `src/components/Pages/DragDropBuilder.jsx` - Added demo mode functionality
3. `src/components/Pages/DemoPage.jsx` - New demo landing page
4. `src/util/Route.jsx` - Added demo route
5. `src/components/Layout/Sidebar.jsx` - Added demo navigation

### Key Functions

- `replaceSlugContent()` - Handles complex slug-based replacements
- `dummyComponents` - Array of pre-built demo components
- `constValue` - Data structure for dynamic content
- `contentTemplates` - Template patterns for content replacement

## Demo Scenarios Supported

### City-based Pages

- Location-specific hero sections
- Population and specialty information
- Contact forms with city context

### Service-based Pages

- Service descriptions and pricing
- Duration and feature information
- Call-to-action buttons

### Mixed Content

- Combination of static and dynamic content
- Nested object access patterns
- Fallback handling for missing data

This implementation provides a complete demonstration of the drag & drop page builder with sophisticated content replacement capabilities, making it easy for users to understand and test the system's functionality.
