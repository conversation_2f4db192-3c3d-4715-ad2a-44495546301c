import React, { useState, useEffect } from "react";
import { Radio, Switch, Select, Button, Drawer, Input } from "antd";
import Header from "../Layout/Header";
import {
  Save,
  X,
  Eye,
  Smartphone,
  Tablet,
  Monitor,
  Plus,
  Settings,
  Trash2,
  GripVertical,
  FileJson,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import {
  replaceContent,
  constValue,
  defaultContent,
} from "../../util/contentReplacer";
import {
  evaluateContentPath,
  demoContent,
  dummyComponents,
  contentTemplates,
  replaceSlugContent,
  replaceContentWithJson,
} from "../../util/demoContent";
import { getPreviewHTML } from "../Components/content";
import generateTailwindCSS from "../../utils/generatePreviewCSS";

const DragDropBuilder = ({ page, onSave, onCancel }) => {
  const api = useHttp();
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pageData, setPageData] = useState({
    name: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    custom_css: "",
    custom_js: "",
    components: [],
  });
  console.log(pageData, "kjdbfjdbjf");
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [previewMode, setPreviewMode] = useState("desktop");
  const [showSettings, setShowSettings] = useState(false);
  const [saving, setSaving] = useState(false);
  const [draggedComponent, setDraggedComponent] = useState(null);

  // Demo mode states
  const [isDemoMode, setIsDemoMode] = useState(!page); // Enable demo mode when no page provided
  const [demoSlug, setDemoSlug] = useState("Ahmedabad");
  const [availableSlugs] = useState([
    "Ahmedabad",
    "Noida",
    "Mumbai",
    "Delhi",
    "web-development",
    "mobile-app",
    "digital-marketing",
  ]);

  // JSON Content Management
  const [showJsonEditor, setShowJsonEditor] = useState(false);
  const [jsonContent, setJsonContent] = useState({
    title: "Welcome to Dream Builder",
    subtitle: "Build Amazing Websites",
    description: "Create stunning websites with our drag-and-drop builder",
    button_text: "Get Started",
    firstName: "John",
    lastName: "Doe",
    age: 30,
    isStudent: false,
    courses: ["History", "Math"],
    address: {
      street: "123 Main St",
      city: "Anytown",
      zipCode: "12345",
    },
    contact: null,
    company_name: "Dream Builder Inc",
    phone: "+****************",
    email: "<EMAIL>",
  });

  useEffect(() => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      console.log("Components fetched:", res);
      // Combine real components with dummy components for demo
      const allComponents = [...res, ...dummyComponents];
      setComponents(allComponents);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories fetched:", res);
      // Add demo categories
      const demoCategories = [
        { id: "demo-headers", name: "Demo Headers", color: "#3B82F6" },
        { id: "demo-location", name: "Demo Location", color: "#10B981" },
        { id: "demo-services", name: "Demo Services", color: "#F59E0B" },
        { id: "demo-contact", name: "Demo Contact", color: "#8B5CF6" },
        { id: "demo-json", name: "Demo JSON", color: "#EF4444" },
        { id: "demo-mixed", name: "Demo Mixed", color: "#F97316" },
      ];
      const allCategories = [...res, ...demoCategories];
      setCategories(allCategories);
    });

    if (page) {
      setPageData({
        name: page.name || "",
        slug: page.slug || demoSlug,
        meta_title: page.meta_title || "",
        meta_description: page.meta_description || "",
        custom_css: page.custom_css || "",
        custom_js: page.custom_js || "",
        components: page.components || [],
      });
    } else {
      // Set demo page data if no page provided
      setPageData({
        name: "Demo Page",
        slug: demoSlug,
        meta_title: "Demo Page - Dream Builder",
        meta_description: "Demo page showcasing drag and drop functionality",
        custom_css: "",
        custom_js: "",
        components: [],
      });
    }
  }, [page, demoSlug]);

  const handleSave = async () => {
    setSaving(true);

    const apiConfig = page
      ? apiGenerator(CONSTANTS.API.pages.update, { id: page.id })
      : CONSTANTS.API.pages.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Page saved successfully:", res);
        setSaving(false);
        onSave();
      },
      pageData,
      page ? "Page updated successfully!" : "Page created successfully!",
      (error) => {
        console.error("Error saving page:", error);
        setSaving(false);
      }
    );
  };

  const addComponentToPage = (component) => {
    const newPageComponent = {
      id: component.id,
      name: component.name,
      order: pageData.components.length,
    };

    setPageData({
      ...pageData,
      components: [...pageData.components, newPageComponent],
    });
  };

  const removeComponentFromPage = (index) => {
    const updatedComponents = pageData.components.filter((_, i) => i !== index);
    setPageData({
      ...pageData,
      components: updatedComponents,
    });
  };

  const moveComponent = (fromIndex, toIndex) => {
    const updatedComponents = [...pageData.components];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setPageData({
      ...pageData,
      components: updatedComponents,
    });
  };

  const [dropTarget, setDropTarget] = useState(null);

  const handleDragStart = (e, component) => {
    setDraggedComponent(component);
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";

    // Find the closest droppable target
    const target = e.target.closest('[data-droppable="true"]');
    if (target) {
      setDropTarget(target.dataset.position);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    if (draggedComponent) {
      const position =
        dropTarget !== null ? parseInt(dropTarget) : pageData.components.length;

      // Create a new component at the specific position
      const newComponents = [...pageData.components];
      newComponents.splice(position, 0, {
        id: draggedComponent.id,
        name: draggedComponent.name,
        order: position,
      });

      // Update all orders
      const updatedComponents = newComponents.map((comp, index) => ({
        ...comp,
        order: index,
      }));

      setPageData({
        ...pageData,
        components: updatedComponents,
      });

      setDraggedComponent(null);
      setDropTarget(null);
    }
  };

  const getPreviewModeStyles = () => {
    switch (previewMode) {
      case "mobile":
        return "tw-w-80 tw-h-96";
      case "tablet":
        return "tw-w-96 tw-h-96";
      default:
        return "tw-w-full tw-h-96";
    }
  };

  const generatePreviewHTML = () => {
    let html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${pageData.meta_title || pageData.name}</title>
        <meta name="description" content="${pageData.meta_description}">
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
         ${generateTailwindCSS()}
          /* Default styles */
          body { margin: 0; padding: 0; }
          /* TailwindCSS basic utilities */
          .container { width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
          /* Component styles */
          ${pageData.custom_css}
          /* Dynamic styles for components */
          ${components
            .filter((comp) =>
              pageData.components.some((pc) => pc.id === comp.id)
            )
            .map((comp) => comp.css_content || "")
            .join("\n")}
        </style>
      </head>
      <body>
    `;

    pageData.components.forEach((pageComp) => {
      const component = components.find((c) => c.id === pageComp.id);
      if (component) {
        let componentHTML = component.html_content;

        // Use enhanced JSON content replacement system
        if (isDemoMode) {
          componentHTML = replaceContentWithJson(
            componentHTML,
            jsonContent,
            pageData.slug || demoSlug,
            constValue,
            contentTemplates
          );
        } else {
          // Use original slug-based content replacement for non-demo mode
          componentHTML = replaceSlugContent(
            componentHTML,
            pageData.slug || demoSlug,
            constValue,
            contentTemplates
          );
        }

        // Replace any remaining placeholders with sample data
        if (component.placeholders) {
          component.placeholders.forEach((placeholder) => {
            // Skip if already replaced by content replacement
            if (!componentHTML.includes(`\${${placeholder}}`)) return;

            const sampleData = getSampleData(placeholder, pageData);
            componentHTML = componentHTML.replace(
              new RegExp(`\\$\\{${placeholder}\\}`, "g"),
              sampleData
            );
          });
        }

        // Process any remaining complex content patterns
        componentHTML = replaceContent(
          componentHTML,
          {
            slug: pageData.slug || demoSlug,
            ...defaultContent,
          },
          { constValue }
        );

        html += componentHTML + "\n";
      }
    });

    html += `
        <script>
          ${pageData.js_content}
        </script>
      </body>
      </html>
    `;

    return html;
  };

  const getSampleData = (placeholder, pageData) => {
    const lowerPlaceholder = placeholder.toLowerCase();

    // If it's a demo content reference (e.g., user.firstName)
    if (placeholder.includes(".")) {
      return evaluateContentPath(placeholder) || `[${placeholder}]`;
    }

    // If it's a slug reference, use the page's slug
    if (placeholder === "slug") {
      return pageData.slug || "sample-slug";
    }

    // Check for complex patterns that need custom handling
    if (placeholder.includes("?.") || placeholder.includes("constValue")) {
      return placeholder; // Keep the placeholder for complex replacements
    }

    // Default sample data
    if (
      lowerPlaceholder.includes("title") ||
      lowerPlaceholder.includes("heading")
    ) {
      return "Sample Page Title";
    } else if (lowerPlaceholder.includes("subtitle")) {
      return "This is a sample subtitle";
    } else if (
      lowerPlaceholder.includes("content") ||
      lowerPlaceholder.includes("text")
    ) {
      return "This is sample content that demonstrates how your page will look with real data.";
    } else if (
      lowerPlaceholder.includes("image") ||
      lowerPlaceholder.includes("img")
    ) {
      return "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=400&fit=crop";
    } else if (
      lowerPlaceholder.includes("button") ||
      lowerPlaceholder.includes("cta")
    ) {
      return "Learn More";
    } else {
      // Check if it's a top-level demo content key
      return demoContent[placeholder]
        ? JSON.stringify(demoContent[placeholder], null, 2)
        : `[${placeholder}]`;
    }
  };

  const groupedComponents = categories.reduce((acc, category) => {
    let filteredComponents;

    if (isDemoMode) {
      // In demo mode, show demo components and regular components
      filteredComponents = components.filter(
        (comp) =>
          comp.category_id === category.id ||
          comp.category_name === category.name
      );
    } else {
      // In normal mode, only show regular components (exclude demo components)
      filteredComponents = components.filter(
        (comp) =>
          comp.category_id === category.id &&
          !comp.id.toString().startsWith("demo-")
      );
    }

    if (filteredComponents.length > 0) {
      acc[category.id] = {
        name: category.name,
        color: category.color,
        components: filteredComponents,
      };
    }
    return acc;
  }, {});

  return (
    <>
      <Header
        title={page ? "Edit Page" : "Create Page"}
        subtitle="Build your page using drag-and-drop components"
      />

      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        {/* Left Sidebar - Components Library */}
        <div className="tw-w-80 tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col">
          <div className="tw-p-4 tw-border-b tw-border-gray-200">
            <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-2">
              Component Library
            </h3>
            <p className="tw-text-sm tw-text-gray-600">
              Drag components to the canvas to build your page
            </p>

            {isDemoMode && (
              <div className="tw-mt-3 tw-p-3 tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-lg">
                <div className="tw-flex tw-items-center tw-mb-2">
                  <Eye className="tw-w-4 tw-h-4 tw-text-blue-600 tw-mr-2" />
                  <span className="tw-text-sm tw-font-medium tw-text-blue-800">
                    Demo Mode Active
                  </span>
                </div>
                <p className="tw-text-xs tw-text-blue-700">
                  Content will be replaced with JSON data and slug:{" "}
                  <strong>{demoSlug}</strong>
                </p>
                <p className="tw-text-xs tw-text-blue-600 tw-mt-1">
                  Try: ${`{title}`}, ${`{firstName}`}, ${`{address.city}`}, $
                  {`{constValue?.city[${`{slug}`}]?.location1}`}
                </p>
                <p className="tw-text-xs tw-text-blue-500 tw-mt-1">
                  Click "JSON Content" button to edit data →
                </p>
              </div>
            )}
          </div>

          <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
            {Object.entries(groupedComponents).map(
              ([categoryId, categoryData]) => (
                <div key={categoryId} className="tw-mb-6">
                  <div className="tw-flex tw-items-center tw-mb-3">
                    <div
                      className="tw-w-3 tw-h-3 tw-rounded-full tw-mr-2"
                      style={{ backgroundColor: categoryData.color }}
                    />
                    <h4 className="tw-font-medium tw-text-gray-900">
                      {categoryData.name}
                    </h4>
                  </div>

                  <div className="tw-space-y-2">
                    {categoryData.components.map((component) => (
                      <div
                        key={component.id}
                        draggable
                        onDragStart={(e) => handleDragStart(e, component)}
                        className="tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-cursor-move tw-hover:tw-bg-gray-100 tw-hover:tw-border-gray-300 tw-transition-colors"
                      >
                        <div className="tw-flex tw-items-center tw-justify-between">
                          <div className="tw-flex-1">
                            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                              {component.name}
                            </p>
                            <p className="tw-text-xs tw-text-gray-500">
                              {component.placeholders
                                ? component.placeholders.length
                                : 0}{" "}
                              placeholders
                            </p>
                          </div>
                          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            )}

            {Object.keys(groupedComponents).length === 0 && (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500">No components available</p>
                <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                  Create components first to use them here
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Main Canvas Area */}
        <div className="tw-flex-1 tw-flex tw-flex-col">
          {/* Top Toolbar */}
          <div className="tw-bg-white tw-border-b tw-border-gray-200 tw-p-4">
            <div className="tw-flex tw-items-center tw-justify-between">
              <div className="tw-flex tw-items-center tw-space-x-4">
                <div className="tw-bg-white tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-200 tw-p-2">
                  <Radio.Group
                    value={previewMode}
                    onChange={(e) => setPreviewMode(e.target.value)}
                    buttonStyle="solid"
                    size="small"
                  >
                    <Radio.Button
                      value="desktop"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Monitor className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                    <Radio.Button
                      value="tablet"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Tablet className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                    <Radio.Button
                      value="mobile"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Smartphone className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                  </Radio.Group>
                </div>

                <button
                  onClick={() => setShowSettings(true)}
                  className="tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
                >
                  <Settings className="tw-w-4 tw-h-4 tw-mr-2" />
                  Page Settings
                </button>

                {/* Demo Mode Controls */}
                <div className="tw-flex tw-items-center tw-space-x-3 tw-bg-gray-50 tw-rounded-lg tw-p-3 tw-border tw-border-gray-200">
                  <div className="tw-flex tw-items-center tw-space-x-2">
                    <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                      Demo Mode:
                    </span>
                    <Switch
                      checked={isDemoMode}
                      onChange={setIsDemoMode}
                      size="small"
                    />
                  </div>
                  {isDemoMode && (
                    <div className="tw-flex tw-items-center tw-space-x-2">
                      <span className="tw-text-sm tw-text-gray-600">Slug:</span>
                      <Select
                        value={demoSlug}
                        onChange={setDemoSlug}
                        size="small"
                        style={{ width: 150 }}
                        options={availableSlugs.map((slug) => ({
                          value: slug,
                          label: slug,
                        }))}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="tw-flex tw-items-center tw-space-x-3">
                {isDemoMode && (
                  <button
                    onClick={() => setShowJsonEditor(true)}
                    className="tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors tw-border tw-border-gray-300"
                  >
                    <FileJson className="tw-w-4 tw-h-4 tw-mr-2" />
                    JSON Content
                  </button>
                )}
                <button
                  onClick={onCancel}
                  className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={saving || !pageData.name || !pageData.slug}
                  className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-disabled:tw-opacity-50"
                >
                  {saving ? (
                    <>
                      <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                      Save Page
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Canvas */}
          <div className="tw-flex-1 tw-bg-gray-100 tw-p-6 tw-overflow-auto">
            <div className="tw-flex tw-justify-center">
              <div
                className={`tw-bg-white tw-shadow-lg tw-rounded-lg tw-overflow-hidden ${getPreviewModeStyles()}`}
              >
                <div className="tw-w-full tw-h-full tw-flex tw-flex-col">
                  {/* Initial drop zone */}
                  <div
                    className={`tw-w-full tw-py-4 tw-flex tw-items-center tw-justify-center tw-border-2 tw-border-dashed ${
                      dropTarget === "0"
                        ? "tw-border-blue-500 tw-bg-blue-50"
                        : "tw-border-gray-300"
                    }`}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    data-droppable="true"
                    data-position="0"
                  >
                    {dropTarget === "0" && (
                      <div className="tw-text-blue-500">
                        <Plus className="tw-w-6 tw-h-6" />
                      </div>
                    )}
                  </div>

                  {pageData.components.length > 0 ? (
                    <>
                      <iframe
                        srcDoc={generatePreviewHTML()}
                        // srcDoc={getPreviewHTML(pageData.components)}
                        className="tw-w-full tw-flex-1 tw-border-0"
                        title="Page Preview"
                      />
                      {/* Drop zones between components */}
                      {pageData.components.map((comp, index) => (
                        <div
                          key={`dropzone-${index + 1}`}
                          className={`tw-w-full tw-py-2 tw-flex tw-items-center tw-justify-center tw-border-2 tw-border-dashed ${
                            dropTarget === (index + 1).toString()
                              ? "tw-border-blue-500 tw-bg-blue-50"
                              : "tw-border-transparent"
                          }`}
                          onDragOver={handleDragOver}
                          onDrop={handleDrop}
                          data-droppable="true"
                          data-position={index + 1}
                        >
                          {dropTarget === (index + 1).toString() && (
                            <div className="tw-text-blue-500">
                              <Plus className="tw-w-4 tw-h-4" />
                            </div>
                          )}
                        </div>
                      ))}
                    </>
                  ) : (
                    <div className="tw-flex-1 tw-flex tw-items-center tw-justify-center">
                      <div className="tw-text-center">
                        <Plus className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                        <p className="tw-text-gray-500 tw-mb-2">
                          Drop components here
                        </p>
                        <p className="tw-text-sm tw-text-gray-400">
                          Drag components from the left panel to build your page
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Page Structure */}
        <div className="tw-w-80 tw-bg-white tw-border-l tw-border-gray-200 tw-flex tw-flex-col">
          <div className="tw-p-4 tw-border-b tw-border-gray-200">
            <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
              Page Structure
            </h3>
            <p className="tw-text-sm tw-text-gray-600 tw-mt-1">
              {pageData.components.length} components
            </p>
          </div>

          <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
            {pageData.components.length > 0 ? (
              <div className="tw-space-y-2">
                {pageData.components.map((pageComp, index) => {
                  const component = components.find(
                    (c) => c.id === pageComp.id
                  );
                  return (
                    <div
                      key={index}
                      className="tw-flex tw-items-center tw-justify-between tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200"
                    >
                      <div className="tw-flex tw-items-center">
                        <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400 tw-mr-2" />
                        <div>
                          <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                            {component?.name || "Unknown Component"}
                          </p>
                          <p className="tw-text-xs tw-text-gray-500">
                            Position {index + 1}
                          </p>
                        </div>
                      </div>

                      <button
                        onClick={() => removeComponentFromPage(index)}
                        className="tw-p-1 tw-text-gray-400 tw-hover:tw-text-red-600 tw-rounded"
                      >
                        <Trash2 className="tw-w-4 tw-h-4" />
                      </button>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500">No components added</p>
                <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                  Components will appear here as you add them
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Page Settings Modal */}
      {showSettings && (
        <div className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-50">
          <div className="tw-bg-white tw-rounded-xl tw-p-6 tw-w-full tw-max-w-2xl tw-m-4 tw-max-h-screen tw-overflow-y-auto">
            <div className="tw-flex tw-justify-between tw-items-center tw-mb-6">
              <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                Page Settings
              </h3>
              <button
                onClick={() => setShowSettings(false)}
                className="tw-text-gray-400 tw-hover:tw-text-gray-600"
              >
                <X className="tw-w-5 tw-h-5" />
              </button>
            </div>

            <div className="tw-space-y-4">
              <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-gap-4">
                <div>
                  <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                    Page Name *
                  </label>
                  <input
                    type="text"
                    value={pageData.name}
                    onChange={(e) =>
                      setPageData({ ...pageData, name: e.target.value })
                    }
                    className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                    placeholder="e.g., Home, About, Contact"
                    required
                  />
                </div>

                <div>
                  <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                    URL Slug *
                  </label>
                  <input
                    type="text"
                    value={pageData.slug}
                    onChange={(e) =>
                      setPageData({ ...pageData, slug: e.target.value })
                    }
                    className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                    placeholder="e.g., home, about, contact"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Meta Title
                </label>
                <input
                  type="text"
                  value={pageData.meta_title}
                  onChange={(e) =>
                    setPageData({ ...pageData, meta_title: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                  placeholder="SEO title for this page"
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Meta Description
                </label>
                <textarea
                  value={pageData.meta_description}
                  onChange={(e) =>
                    setPageData({
                      ...pageData,
                      meta_description: e.target.value,
                    })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-20"
                  placeholder="Brief description for search engines"
                  rows="3"
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Custom CSS
                </label>
                <textarea
                  value={pageData.custom_css}
                  onChange={(e) =>
                    setPageData({ ...pageData, custom_css: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-32 tw-font-mono tw-text-sm"
                  placeholder="/* Custom CSS for this page */"
                  rows="6"
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Custom JavaScript
                </label>
                <textarea
                  value={pageData.custom_js}
                  onChange={(e) =>
                    setPageData({ ...pageData, custom_js: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-32 tw-font-mono tw-text-sm"
                  placeholder="// Custom JavaScript for this page"
                  rows="6"
                />
              </div>
            </div>

            <div className="tw-flex tw-justify-end tw-space-x-3 tw-mt-6">
              <button
                onClick={() => setShowSettings(false)}
                className="tw-px-4 tw-py-2 tw-border tw-border-gray-300 tw-text-gray-700 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-50 tw-transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* JSON Content Editor Drawer */}
      <Drawer
        title="JSON Content Editor"
        placement="right"
        width={500}
        open={showJsonEditor}
        onClose={() => setShowJsonEditor(false)}
        extra={
          <div className="tw-flex tw-items-center tw-space-x-2">
            <span className="tw-text-sm tw-text-gray-600">Slug:</span>
            <Select
              value={demoSlug}
              onChange={setDemoSlug}
              size="small"
              style={{ width: 120 }}
              options={availableSlugs.map((slug) => ({
                value: slug,
                label: slug,
              }))}
            />
          </div>
        }
      >
        <div className="tw-space-y-4">
          <div className="tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-lg tw-p-4">
            <h4 className="tw-text-sm tw-font-semibold tw-text-blue-800 tw-mb-2">
              How to use JSON Content:
            </h4>
            <ul className="tw-text-xs tw-text-blue-700 tw-space-y-1">
              <li>
                • Use ${`{key}`} for simple replacement: ${`{title}`}
              </li>
              <li>
                • Use ${`{object.property}`} for nested: ${`{address.city}`}
              </li>
              <li>• Use ${`{slug}`} for current slug value</li>
              <li>
                • Use ${`{constValue?.city[${`{slug}`}]?.location1}`} for
                dynamic data
              </li>
            </ul>
          </div>

          <div>
            <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
              JSON Content (Edit to see real-time changes)
            </label>
            <Input.TextArea
              value={JSON.stringify(jsonContent, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  setJsonContent(parsed);
                } catch (error) {
                  // Keep the text as is if invalid JSON
                }
              }}
              rows={20}
              className="tw-font-mono tw-text-sm"
              placeholder="Enter your JSON content here..."
            />
          </div>

          <div className="tw-bg-gray-50 tw-border tw-border-gray-200 tw-rounded-lg tw-p-4">
            <h4 className="tw-text-sm tw-font-semibold tw-text-gray-800 tw-mb-2">
              Example Patterns:
            </h4>
            <div className="tw-text-xs tw-text-gray-600 tw-space-y-1 tw-font-mono">
              <div>
                ${`{title}`} → {jsonContent.title}
              </div>
              <div>
                ${`{firstName}`} → {jsonContent.firstName}
              </div>
              <div>
                ${`{address.city}`} → {jsonContent.address?.city}
              </div>
              <div>
                ${`{slug}`} → {demoSlug}
              </div>
            </div>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default DragDropBuilder;
