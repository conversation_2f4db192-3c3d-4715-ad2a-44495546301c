export const demoContent = {
  user: {
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    age: 30,
    isStudent: false,
    courses: ["History", "Math"],
    address: {
      street: "123 Main St",
      city: "Anytown",
      zipCode: "12345"
    },
    contact: null
  },
  company: {
    name: "Dream Builder",
    industry: "Technology",
    locations: ["New York", "London", "Tokyo"],
    departments: {
      engineering: {
        head: "<PERSON>",
        employees: 50
      },
      marketing: {
        head: "<PERSON>",
        employees: 30
      }
    }
  },
  products: [
    {
      id: 1,
      name: "Website Builder",
      price: 99.99,
      features: ["Drag & Drop", "SEO Friendly", "Responsive"]
    },
    {
      id: 2,
      name: "E-commerce Solution",
      price: 199.99,
      features: ["Shopping Cart", "Payment Gateway", "Inventory Management"]
    }
  ],
  demoAssets: {
    html: `<div class="demo-html">
      <h1>Welcome to the Demo Page</h1>
      <p>This is a sample HTML content block for preview.</p>
    </div>`,
    css: `.demo-html { background: #f0f4f8; padding: 24px; border-radius: 8px; }
      .demo-html h1 { color: #2563eb; font-size: 2rem; }
      .demo-html p { color: #334155; }`,
    js: `document.addEventListener('DOMContentLoaded', function() {
      const el = document.querySelector('.demo-html h1');
      if (el) el.innerText += ' (JS Enhanced)';
    });`
  }
};

export const evaluateContentPath = (path, data = demoContent) => {
    try {
        // Handle array access with index
        if (path.includes('[') && path.includes(']')) {
            const parts = path.split(/[\[\].]/).filter(Boolean);
            return parts.reduce((obj, key) => obj?.[isNaN(key) ? key : parseInt(key)], data);
        }

        // Handle dot notation
        return path.split('.').reduce((obj, key) => obj?.[key], data);
    } catch (error) {
        console.error(`Error evaluating path ${path}:`, error);
        return `[${path}]`;
    }
};
