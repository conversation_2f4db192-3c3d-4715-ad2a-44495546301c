export const demoContent = {
    user: {
        firstName: "<PERSON>",
        lastName: "<PERSON><PERSON>",
        age: 30,
        isStudent: false,
        courses: ["History", "Math"],
        address: {
            street: "123 Main St",
            city: "Anytown",
            zipCode: "12345"
        },
        contact: null
    },
    company: {
        name: "Dream Builder",
        industry: "Technology",
        locations: ["New York", "London", "Tokyo"],
        departments: {
            engineering: {
                head: "<PERSON>",
                employees: 50
            },
            marketing: {
                head: "<PERSON>",
                employees: 30
            }
        }
    },
    products: [
        {
            id: 1,
            name: "Website Builder",
            price: 99.99,
            features: ["Drag & Drop", "SEO Friendly", "Responsive"]
        },
        {
            id: 2,
            name: "E-commerce Solution",
            price: 199.99,
            features: ["Shopping Cart", "Payment Gateway", "Inventory Management"]
        }
    ]
};

// Slug-based content replacement system
export const constValue = {
    city: {
        Ahmedabad: {
            location1: "Gujarat's Commercial Hub",
            location2: "Sabarmati Riverfront",
            population: "8.2 million",
            specialty: "Textile Industry"
        },
        Noida: {
            location1: "NCR Tech Hub",
            location2: "Sector 62",
            population: "1.2 million",
            specialty: "IT Services"
        },
        Mumbai: {
            location1: "Financial Capital",
            location2: "Marine Drive",
            population: "20.4 million",
            specialty: "Bollywood & Finance"
        },
        Delhi: {
            location1: "National Capital",
            location2: "India Gate",
            population: "32.9 million",
            specialty: "Government & Politics"
        }
    },
    service: {
        "web-development": {
            title: "Web Development",
            description: "Custom websites and web applications",
            price: "$2,999",
            duration: "4-6 weeks"
        },
        "mobile-app": {
            title: "Mobile App Development",
            description: "iOS and Android applications",
            price: "$4,999",
            duration: "8-12 weeks"
        },
        "digital-marketing": {
            title: "Digital Marketing",
            description: "SEO, PPC, and social media marketing",
            price: "$1,999",
            duration: "3-6 months"
        }
    }
};

export const contentTemplates = {
    // Static content templates
    title: "Welcome to Dream Builder",
    subtitle: "Build Amazing Websites",
    description: "Create stunning websites with our drag-and-drop builder",
    button_text: "Get Started",

    // Dynamic content templates with slug support
    city_hero_title: "Hello, ${slug}, ${constValue?.city[${slug}]?.location1}",
    city_hero_subtitle: "Discover ${constValue?.city[${slug}]?.location2}",
    city_population: "Population: ${constValue?.city[${slug}]?.population}",
    city_specialty: "Known for: ${constValue?.city[${slug}]?.specialty}",

    service_hero_title: "We are providing ${slug} services",
    service_description: "${constValue?.service[${slug}]?.description}",
    service_price: "Starting at ${constValue?.service[${slug}]?.price}",
    service_duration: "Timeline: ${constValue?.service[${slug}]?.duration}",

    // Common content patterns
    contact_title: "Contact us in ${slug}",
    about_title: "About our ${slug} services",
    testimonial_location: "Client from ${constValue?.city[${slug}]?.location1}"
};

// Dummy components for demo
export const dummyComponents = [
    {
        id: 'demo-hero-1',
        name: 'Hero Section',
        category_name: 'Demo Headers',
        category_color: '#3B82F6',
        html_content: `
<div class="tw-bg-gradient-to-r tw-from-blue-500 tw-to-purple-600 tw-py-20">
    <div class="tw-container tw-mx-auto tw-px-4 tw-text-center">
        <h1 class="tw-text-5xl tw-font-bold tw-text-white tw-mb-6">\${title}</h1>
        <p class="tw-text-xl tw-text-white tw-opacity-90 tw-mb-8">\${description}</p>
        <button class="tw-bg-white tw-text-blue-600 tw-px-8 tw-py-3 tw-rounded-lg tw-font-semibold tw-hover:tw-bg-gray-100 tw-transition-colors">
            \${button_text}
        </button>
    </div>
</div>`,
        css_content: '',
        js_content: '',
        placeholders: ['title', 'description', 'button_text'],
        version: 1
    },
    {
        id: 'demo-city-hero',
        name: 'City Hero Section',
        category_name: 'Demo Location',
        category_color: '#10B981',
        html_content: `
<div class="tw-bg-gradient-to-r tw-from-green-500 tw-to-blue-500 tw-py-20">
    <div class="tw-container tw-mx-auto tw-px-4 tw-text-center">
        <h1 class="tw-text-5xl tw-font-bold tw-text-white tw-mb-4">\${city_hero_title}</h1>
        <h2 class="tw-text-2xl tw-text-white tw-opacity-90 tw-mb-6">\${city_hero_subtitle}</h2>
        <div class="tw-flex tw-justify-center tw-space-x-8 tw-text-white">
            <div class="tw-text-center">
                <p class="tw-text-lg tw-font-semibold">\${city_population}</p>
            </div>
            <div class="tw-text-center">
                <p class="tw-text-lg tw-font-semibold">\${city_specialty}</p>
            </div>
        </div>
    </div>
</div>`,
        css_content: '',
        js_content: '',
        placeholders: ['city_hero_title', 'city_hero_subtitle', 'city_population', 'city_specialty'],
        version: 1
    },
    {
        id: 'demo-service-card',
        name: 'Service Card',
        category_name: 'Demo Services',
        category_color: '#F59E0B',
        html_content: `
<div class="tw-bg-white tw-rounded-lg tw-shadow-lg tw-p-8 tw-m-4">
    <h3 class="tw-text-2xl tw-font-bold tw-text-gray-800 tw-mb-4">\${service_hero_title}</h3>
    <p class="tw-text-gray-600 tw-mb-4">\${service_description}</p>
    <div class="tw-flex tw-justify-between tw-items-center tw-mb-6">
        <span class="tw-text-3xl tw-font-bold tw-text-blue-600">\${service_price}</span>
        <span class="tw-text-sm tw-text-gray-500">\${service_duration}</span>
    </div>
    <button class="tw-w-full tw-bg-blue-600 tw-text-white tw-py-3 tw-rounded-lg tw-font-semibold tw-hover:tw-bg-blue-700 tw-transition-colors">
        Get Quote
    </button>
</div>`,
        css_content: '',
        js_content: '',
        placeholders: ['service_hero_title', 'service_description', 'service_price', 'service_duration'],
        version: 1
    },
    {
        id: 'demo-contact',
        name: 'Contact Section',
        category_name: 'Demo Contact',
        category_color: '#8B5CF6',
        html_content: `
<div class="tw-bg-gray-50 tw-py-16">
    <div class="tw-container tw-mx-auto tw-px-4">
        <div class="tw-text-center tw-mb-12">
            <h2 class="tw-text-4xl tw-font-bold tw-text-gray-800 tw-mb-4">\${contact_title}</h2>
            <p class="tw-text-xl tw-text-gray-600">\${testimonial_location}</p>
        </div>
        <div class="tw-max-w-md tw-mx-auto tw-bg-white tw-rounded-lg tw-shadow-lg tw-p-8">
            <form class="tw-space-y-4">
                <input type="text" placeholder="Your Name" class="tw-w-full tw-px-4 tw-py-3 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-outline-none tw-focus:tw-border-blue-500">
                <input type="email" placeholder="Your Email" class="tw-w-full tw-px-4 tw-py-3 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-outline-none tw-focus:tw-border-blue-500">
                <textarea placeholder="Your Message" rows="4" class="tw-w-full tw-px-4 tw-py-3 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-outline-none tw-focus:tw-border-blue-500"></textarea>
                <button type="submit" class="tw-w-full tw-bg-purple-600 tw-text-white tw-py-3 tw-rounded-lg tw-font-semibold tw-hover:tw-bg-purple-700 tw-transition-colors">
                    Send Message
                </button>
            </form>
        </div>
    </div>
</div>`,
        css_content: '',
        js_content: '',
        placeholders: ['contact_title', 'testimonial_location'],
        version: 1
    }
];

export const evaluateContentPath = (path, data = demoContent) => {
    try {
        // Handle array access with index
        if (path.includes('[') && path.includes(']')) {
            const parts = path.split(/[\[\].]/).filter(Boolean);
            return parts.reduce((obj, key) => obj?.[isNaN(key) ? key : parseInt(key)], data);
        }

        // Handle dot notation
        return path.split('.').reduce((obj, key) => obj?.[key], data);
    } catch (error) {
        console.error(`Error evaluating path ${path}:`, error);
        return `[${path}]`;
    }
};

// Enhanced content replacement function for slug-based patterns
export const replaceSlugContent = (content, slug = 'Ahmedabad', constants = constValue, templates = contentTemplates) => {
    let processedContent = content;

    // First, replace simple template references
    Object.keys(templates).forEach(key => {
        const pattern = new RegExp(`\\$\\{${key}\\}`, 'g');
        processedContent = processedContent.replace(pattern, templates[key]);
    });

    // Then process complex slug-based patterns
    // Pattern: ${constValue?.city[${slug}]?.location1}
    processedContent = processedContent.replace(/\$\{constValue\?\.([\w]+)\[\$\{slug\}\]\?\.([\w]+)\}/g, (match, category, property) => {
        try {
            return constants[category]?.[slug]?.[property] || `[${category}.${slug}.${property}]`;
        } catch (error) {
            return match;
        }
    });

    // Replace ${slug} references
    processedContent = processedContent.replace(/\$\{slug\}/g, slug);

    return processedContent;
};
