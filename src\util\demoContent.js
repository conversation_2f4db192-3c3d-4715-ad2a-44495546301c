export const demoContent = {
    user: {
        firstName: "<PERSON>",
        lastName: "<PERSON><PERSON>",
        age: 30,
        isStudent: false,
        courses: ["History", "Math"],
        address: {
            street: "123 Main St",
            city: "Anytown",
            zipCode: "12345"
        },
        contact: null
    },
    company: {
        name: "Dream Builder",
        industry: "Technology",
        locations: ["New York", "London", "Tokyo"],
        departments: {
            engineering: {
                head: "<PERSON>",
                employees: 50
            },
            marketing: {
                head: "<PERSON>",
                employees: 30
            }
        }
    },
    products: [
        {
            id: 1,
            name: "Website Builder",
            price: 99.99,
            features: ["Drag & Drop", "SEO Friendly", "Responsive"]
        },
        {
            id: 2,
            name: "E-commerce Solution",
            price: 199.99,
            features: ["Shopping Cart", "Payment Gateway", "Inventory Management"]
        }
    ]
};

export const evaluateContentPath = (path, data = demoContent) => {
    try {
        // Handle array access with index
        if (path.includes('[') && path.includes(']')) {
            const parts = path.split(/[\[\].]/).filter(Boolean);
            return parts.reduce((obj, key) => obj?.[isNaN(key) ? key : parseInt(key)], data);
        }

        // Handle dot notation
        return path.split('.').reduce((obj, key) => obj?.[key], data);
    } catch (error) {
        console.error(`Error evaluating path ${path}:`, error);
        return `[${path}]`;
    }
};
