import React, { useState, useEffect } from "react";
import Sidebar from "../Layout/Sidebar";
import Header from "../Layout/Header";
import { Edit2, Trash2, Palette, Plus } from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import {
  Button,
  Spin,
  Card,
  Modal,
  Form,
  Input,
  Typography,
  Space,
  Row,
  Col,
  Empty,
  Popconfirm,
  message,
  Tag,
  Tooltip,
  Divider,
} from "antd";
import {
  PlusOutlined,
  FolderOpenOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import Categorymodal from "./CategoryComponent/Categorymodal";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const CategoryManager = () => {
  const [categories, setCategories] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [saving, setSaving] = useState(false);
  const api = useHttp();
  const [form] = Form.useForm();

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color: "#3B82F6",
  });

  useEffect(() => {
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log(res, "res");
      // const data = res?.json();
      setCategories(res);
    });
    // fetchCategories();
  }, []);

  const handleSubmit = async (values) => {
    try {
      setSaving(true);

      const submitData = {
        ...values,
        color: formData.color, // Include color from state
      };

      const apiConfig = editingCategory
        ? apiGenerator(CONSTANTS.API.categories.update, {
            id: editingCategory.id,
          })
        : CONSTANTS.API.categories.create;

      api.sendRequest(
        apiConfig,
        (res) => {
          console.log("Category saved successfully:", res);
          message.success(
            `Category ${editingCategory ? "updated" : "created"} successfully!`
          );
          // Refresh categories list
          api.sendRequest(CONSTANTS.API.categories.get, (res) => {
            setCategories(res);
          });
          resetForm();
          setSaving(false);
        },
        submitData,
        null, // Remove duplicate message
        (error) => {
          console.error("Error saving category:", error);
          message.error("Failed to save category. Please try again.");
          setSaving(false);
        }
      );
    } catch (error) {
      console.error("Form validation failed:", error);
      setSaving(false);
    }
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    const initialData = {
      name: category.name,
      description: category.description || "",
    };
    setFormData({
      ...initialData,
      color: category.color || "#3B82F6",
    });
    form.setFieldsValue(initialData);
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    api.sendRequest(
      apiGenerator(CONSTANTS.API.categories.delete, { id }),
      (res) => {
        console.log("Category deleted successfully:", res);
        message.success("Category deleted successfully!");
        // Refresh categories list
        api.sendRequest(CONSTANTS.API.categories.get, (res) => {
          setCategories(res);
        });
      },
      null,
      null, // Remove duplicate message
      (error) => {
        console.error("Error deleting category:", error);
        message.error("Failed to delete category. Please try again.");
      }
    );
  };

  const resetForm = () => {
    form.resetFields();
    setFormData({
      name: "",
      description: "",
      color: "#3B82F6",
    });
    setEditingCategory(null);
    setShowForm(false);
    setSaving(false);
  };

  const predefinedColors = [
    "#3B82F6",
    "#8B5CF6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#06B6D4",
    "#84CC16",
    "#F97316",
  ];

  if (api.isLoading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-h-screen tw-w-full">
        {/* <div className="tw-text-center"> */}
        <Spin size="large" />
        {/* </div> */}
      </div>
    );
  }

  return (
    <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header Section */}
        <div className="tw-mb-8">
          <div className="tw-flex tw-justify-between tw-items-center">
            <div>
              <Title level={3} className="!tw-mb-0">
                Category Management ({categories.length})
              </Title>
            </div>

            <Button
              type="primary"
              size="large"
              icon={<Plus />}
              onClick={() => setShowForm(true)}
              className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              // className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-0 tw-h-12 tw-px-6 tw-rounded-lg tw-font-medium hover:tw-from-blue-700 hover:tw-to-purple-700"
            >
              Add Category
            </Button>
          </div>
          <Divider className="tw-my-6" />
        </div>

        {/* Category Form Modal */}
        <Categorymodal
          showForm={showForm}
          setShowForm={setShowForm}
          formData={formData}
          setFormData={setFormData}
          editingCategory={editingCategory}
          setEditingCategory={setEditingCategory}
          predefinedColors={predefinedColors}
          saving={saving}
          handleSubmit={handleSubmit}
          form={form}
          resetForm={resetForm}
        />

        {/* Categories Grid */}
        {categories.length === 0 ? (
          <Card className="tw-text-center tw-py-16">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Text
                    type="secondary"
                    className="tw-text-lg tw-block tw-mb-2"
                  >
                    No categories found
                  </Text>
                  <Text type="secondary">
                    Create your first category to organize your components
                  </Text>
                </div>
              }
            >
              <Button
                type="primary"
                size="large"
                icon={<PlusOutlined />}
                onClick={() => setShowForm(true)}
                className="tw-mt-4 tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-0"
              >
                Create First Category
              </Button>
            </Empty>
          </Card>
        ) : (
          <Row gutter={[24, 24]}>
            {categories?.map((category) => (
              <Col xs={24} sm={12} lg={12} xl={6} key={category.id}>
                <Card
                  className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-1 tw-border-gray-200 tw-rounded-[20px]"
                  styles={{
                    body: {
                      padding: "24px",
                    },
                  }}
                >
                  <div className="tw-mb-4">
                    <div className="tw-flex tw-items-center tw-justify-between">
                      <div className="tw-flex tw-items-center tw-mb-3">
                        <div
                          className="tw-w-4 tw-h-4 tw-rounded-full tw-mr-3 tw-flex-shrink-0"
                          style={{
                            backgroundColor: category.color || "#3B82F6",
                          }}
                        />
                        <Title
                          level={4}
                          className="!tw-mb-0 tw-text-gray-900 tw-truncate"
                        >
                          {category.name}
                        </Title>
                      </div>
                      <div>
                        <Tooltip title="Edit Category" key="edit">
                          <Button
                            type="text"
                            icon={<Edit2 className="tw-w-4 tw-h-4" />}
                            onClick={() => handleEdit(category)}
                            className="tw-text-gray-500 hover:tw-text-blue-600"
                          />
                        </Tooltip>

                        <Tooltip title="Delete Category" key="delete">
                          <Popconfirm
                            title="Delete Category"
                            description="Are you sure you want to delete this category?"
                            onConfirm={() => handleDelete(category.id)}
                            okText="Yes"
                            cancelText="No"
                            okButtonProps={{
                              danger: true,
                            }}
                          >
                            <Button
                              type="text"
                              danger
                              icon={<Trash2 className="tw-w-4 tw-h-4" />}
                              className="tw-text-gray-500 hover:tw-text-red-600"
                            />
                          </Popconfirm>
                        </Tooltip>
                      </div>
                    </div>

                    {category.description && (
                      <Paragraph
                        ellipsis={{ rows: 2, expandable: false }}
                        className="tw-text-gray-600 tw-text-sm tw-mb-4"
                      >
                        {category.description}
                      </Paragraph>
                    )}
                  </div>

                  <div className="tw-flex tw-items-center tw-justify-between tw-pt-4 tw-border-gray-100">
                    <div className="tw-flex tw-items-center tw-gap-2">
                      <Text type="secondary" className="tw-text-xs">
                        ID: {category?.id}
                      </Text>
                      {/* <Tag
                        color={category.color || "#3B82F6"}
                        className="tw-rounded-full tw-px-3 tw-py-1"
                      >
                        {category.components_count || 0} component
                        {(category.components_count || 0) !== 1 ? "s" : ""}
                      </Tag> */}
                    </div>
                    <Text type="secondary" className="tw-text-xs">
                      {new Date(category.created_at).toLocaleDateString()}
                    </Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </div>
    </div>
  );
};

export default CategoryManager;
