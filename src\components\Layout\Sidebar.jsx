import { NavLink } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useSidebar } from "../../contexts/SidebarContext";
import logoImg from "/img/dream-logo.png";
import {
  LayoutDashboard,
  FolderTree,
  Component as Components,
  FileText,
  Layout,
  Globe,
  Users,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Activity,
} from "lucide-react";
import { Button, Image, Drawer, Tooltip } from "antd";

const Sidebar = () => {
  const { user, logout } = useAuth();
  const {
    isCollapsed,
    isMobile,
    isMobileOpen,
    toggleCollapse,
    closeMobileMenu,
  } = useSidebar();

  const navigationItems = [
    {
      name: "Dashboard",
      href: "/",
      icon: LayoutDashboard,
      adminOnly: false,
    },
    {
      name: "Categories",
      href: "/categories",
      icon: FolderTree,
      adminOnly: true,
    },
    {
      name: "Components",
      href: "/components",
      icon: Components,
      adminOnly: false,
    },
    {
      name: "Pages",
      href: "/pages",
      icon: FileText,
      adminOnly: false,
    },
    {
      name: "Templates",
      href: "/templates",
      icon: Layout,
      adminOnly: false,
    },
    {
      name: "Websites",
      href: "/websites",
      icon: Globe,
      adminOnly: false,
    },
    {
      name: "Activity Logs",
      href: "/activity-logs",
      icon: Activity,
      adminOnly: true,
    },
    {
      name: "Users",
      href: "/users",
      icon: Users,
      adminOnly: true,
    },
  ];

  const filteredItems = navigationItems.filter(
    (item) => !item.adminOnly || user?.role === "admin"
  );

  // Desktop Sidebar Component
  const DesktopSidebar = () => (
    <div
      className={`sidebar-desktop tw-bg-white tw-shadow-lg tw-h-screen tw-fixed tw-left-0 tw-top-0 tw-z-40 sidebar-collapse-transition ${
        isCollapsed ? "tw-w-20" : "tw-w-64"
      }`}
    >
      {/* Logo Section */}
      <div
        className={`tw-border-b tw-border-gray-200 tw-relative ${
          isCollapsed ? "tw-p-4" : "tw-p-[25px]"
        }`}
      >
        <div className="tw-flex tw-items-center tw-justify-center">
          {!isCollapsed && (
            <Image
              preview={false}
              src={logoImg}
              alt="Dream Builder"
              className="tw-w-auto tw-h-auto tw-aspect-auto tw-object-contain"
            />
          )}
          {isCollapsed && (
            <div className="tw-w-12 tw-h-12 tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-rounded-lg tw-flex tw-items-center tw-justify-center">
              <span className="tw-text-white tw-font-bold tw-text-sm">DB</span>
            </div>
          )}
        </div>

        {/* Collapse Button */}
        <Tooltip
          title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          placement="right"
        >
          <Button
            type="primary"
            shape="circle"
            size="small"
            icon={
              isCollapsed ? (
                <ChevronRight size={14} />
              ) : (
                <ChevronLeft size={14} />
              )
            }
            onClick={toggleCollapse}
            className="sidebar-collapse-btn tw-absolute tw-right-[-12px] tw-top-[83%] tw-bg-[#2563EB] hover:!tw-bg-[#2563EB] tw-border-[#2563EB] tw-shadow-lg"
            style={{
              width: "24px",
              height: "24px",
              minWidth: "24px",
              // display: "flex",
              // alignItems: "center",
              // justifyContent: "center",
            }}
          />
        </Tooltip>
      </div>

      {/* Navigation */}
      <nav className={`tw-mt-6 ${isCollapsed ? "tw-px-2" : "tw-px-4"}`}>
        <div className="tw-space-y-2">
          {filteredItems.map((item) => (
            <Tooltip
              key={item.name}
              title={isCollapsed ? item.name : ""}
              placement="right"
              mouseEnterDelay={0.5}
            >
              <NavLink
                to={item.href}
                className={({ isActive }) =>
                  `sidebar-nav-item tw-flex tw-items-center tw-text-sm tw-font-medium tw-rounded-lg tw-transition-all tw-duration-200 ${
                    isCollapsed
                      ? "tw-px-3 tw-py-3 tw-justify-center"
                      : "tw-px-4 tw-py-3"
                  } ${
                    isActive
                      ? "tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-shadow-lg"
                      : "tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-900"
                  }`
                }
              >
                <item.icon
                  className={`tw-w-5 tw-h-5 ${!isCollapsed ? "tw-mr-3" : ""}`}
                />
                {!isCollapsed && item.name}
              </NavLink>
            </Tooltip>
          ))}
        </div>
      </nav>

      {/* Logout Button */}
      <div
        className={`tw-absolute tw-bottom-0 tw-w-full tw-border-t tw-border-gray-200 ${
          isCollapsed ? "tw-p-2" : "tw-p-4"
        }`}
      >
        <Tooltip title={isCollapsed ? "Log Out" : ""} placement="right">
          <button
            onClick={logout}
            className={`!tw-text-[#FB3748] tw-flex tw-items-center tw-w-full tw-text-sm tw-hover:tw-bg-gray-200 tw-rounded-lg tw-transition-colors ${
              isCollapsed
                ? "tw-px-3 tw-py-3 tw-justify-center"
                : "tw-px-3 tw-py-2"
            }`}
          >
            <LogOut
              color="#FB3748"
              className={`tw-w-4 tw-h-4 ${!isCollapsed ? "tw-mr-2" : ""}`}
            />
            {!isCollapsed && "Log Out"}
          </button>
        </Tooltip>
      </div>
    </div>
  );

  // Mobile Sidebar Component (Drawer)
  const MobileSidebar = () => (
    <Drawer
      title={
        <div className="tw-flex tw-items-center">
          <Image
            preview={false}
            src={logoImg}
            alt="Dream Builder"
            className="tw-w-auto tw-h-auto tw-aspect-auto tw-object-contain"
            style={{ maxHeight: "32px" }}
          />
        </div>
      }
      placement="left"
      onClose={closeMobileMenu}
      open={isMobileOpen}
      width={280}
      className="tw-mobile-sidebar"
      styles={{
        body: { padding: 0 },
        header: { borderBottom: "1px solid #f0f0f0" },
      }}
    >
      <nav className="tw-mt-4 tw-px-4">
        <div className="tw-space-y-2">
          {filteredItems.map((item) => (
            <NavLink
              key={item.name}
              to={item.href}
              onClick={closeMobileMenu}
              className={({ isActive }) =>
                `tw-flex tw-items-center tw-px-4 tw-py-3 tw-text-sm tw-font-medium tw-rounded-lg tw-transition-all tw-duration-200 ${
                  isActive
                    ? "tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-shadow-lg"
                    : "tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-900"
                }`
              }
            >
              <item.icon className="tw-w-5 tw-h-5 tw-mr-3" />
              {item.name}
            </NavLink>
          ))}
        </div>
      </nav>

      <div className="tw-absolute tw-bottom-0 tw-w-full tw-p-4 tw-border-t tw-border-gray-200">
        <button
          onClick={() => {
            logout();
            closeMobileMenu();
          }}
          className="!tw-text-[#FB3748] tw-flex tw-items-center tw-w-full tw-px-3 tw-py-2 tw-text-sm tw-hover:tw-bg-gray-200 tw-rounded-lg tw-transition-colors"
        >
          <LogOut color="#FB3748" className="tw-w-4 tw-h-4 tw-mr-2" />
          Log Out
        </button>
      </div>
    </Drawer>
  );

  // Main render - show desktop or mobile sidebar based on screen size
  return (
    <>
      {!isMobile && <DesktopSidebar />}
      {isMobile && <MobileSidebar />}
    </>
  );
};

export default Sidebar;
