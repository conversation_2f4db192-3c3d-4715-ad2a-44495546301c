import React, { useState } from "react";
import DragDropBuilder from "./DragDropBuilder";
import Header from "../Layout/Header";
import { Play, ArrowLeft } from "lucide-react";
import { Button } from "antd";

const DemoPage = () => {
  const [showDemo, setShowDemo] = useState(false);

  if (showDemo) {
    return (
      <DragDropBuilder
        page={null} // No existing page - pure demo mode
        onSave={() => {
          // Demo save - just show success message
          alert("Demo saved! In real mode, this would save to the database.");
        }}
        onCancel={() => setShowDemo(false)}
      />
    );
  }

  return (
    <>
      <Header
        title="Demo Page Builder"
        subtitle="Experience the drag-and-drop functionality with sample components"
      />
      
      <div className="tw-min-h-screen tw-bg-gray-50 tw-flex tw-items-center tw-justify-center tw-p-6">
        <div className="tw-max-w-4xl tw-mx-auto">
          <div className="tw-bg-white tw-rounded-xl tw-shadow-lg tw-p-8 tw-text-center">
            <div className="tw-mb-8">
              <div className="tw-w-20 tw-h-20 tw-bg-gradient-to-r tw-from-blue-500 tw-to-purple-600 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-6">
                <Play className="tw-w-10 tw-h-10 tw-text-white" />
              </div>
              <h1 className="tw-text-4xl tw-font-bold tw-text-gray-900 tw-mb-4">
                Page Builder Demo
              </h1>
              <p className="tw-text-xl tw-text-gray-600 tw-mb-8">
                Experience our powerful drag-and-drop page builder with dynamic content replacement
              </p>
            </div>

            <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-gap-8 tw-mb-8">
              <div className="tw-bg-blue-50 tw-rounded-lg tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-blue-800 tw-mb-3">
                  🎯 Drag & Drop Components
                </h3>
                <p className="tw-text-blue-700 tw-text-sm">
                  Drag components from the sidebar and drop them line by line to build your page
                </p>
              </div>
              
              <div className="tw-bg-green-50 tw-rounded-lg tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-green-800 tw-mb-3">
                  🔄 Dynamic Content Replacement
                </h3>
                <p className="tw-text-green-700 tw-text-sm">
                  Content automatically adapts based on slug patterns like ${`{city_hero_title}`}
                </p>
              </div>
              
              <div className="tw-bg-purple-50 tw-rounded-lg tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-purple-800 tw-mb-3">
                  📱 Responsive Preview
                </h3>
                <p className="tw-text-purple-700 tw-text-sm">
                  Preview your page in desktop, tablet, and mobile views instantly
                </p>
              </div>
              
              <div className="tw-bg-orange-50 tw-rounded-lg tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-orange-800 tw-mb-3">
                  🎨 Demo Components
                </h3>
                <p className="tw-text-orange-700 tw-text-sm">
                  Pre-built components with smart placeholders for quick demonstration
                </p>
              </div>
            </div>

            <div className="tw-bg-gray-50 tw-rounded-lg tw-p-6 tw-mb-8">
              <h3 className="tw-text-lg tw-font-semibold tw-text-gray-800 tw-mb-4">
                Demo Features You'll Experience:
              </h3>
              <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-gap-4 tw-text-sm tw-text-gray-600">
                <div className="tw-flex tw-items-center">
                  <span className="tw-w-2 tw-h-2 tw-bg-blue-500 tw-rounded-full tw-mr-3"></span>
                  Static content replacement: ${`{title}`} → "Welcome to Dream Builder"
                </div>
                <div className="tw-flex tw-items-center">
                  <span className="tw-w-2 tw-h-2 tw-bg-green-500 tw-rounded-full tw-mr-3"></span>
                  Slug-based content: ${`{city_hero_title}`} → "Hello, Ahmedabad, Gujarat's Commercial Hub"
                </div>
                <div className="tw-flex tw-items-center">
                  <span className="tw-w-2 tw-h-2 tw-bg-purple-500 tw-rounded-full tw-mr-3"></span>
                  Dynamic data: ${`{constValue?.city[${`{slug}`}]?.location1}`}
                </div>
                <div className="tw-flex tw-items-center">
                  <span className="tw-w-2 tw-h-2 tw-bg-orange-500 tw-rounded-full tw-mr-3"></span>
                  Service patterns: ${`{service_price}`} → "Starting at $2,999"
                </div>
              </div>
            </div>

            <div className="tw-flex tw-flex-col tw-sm:tw-flex-row tw-gap-4 tw-justify-center">
              <Button
                type="primary"
                size="large"
                onClick={() => setShowDemo(true)}
                className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-none tw-h-12 tw-px-8 tw-text-lg tw-font-semibold"
                icon={<Play className="tw-w-5 tw-h-5" />}
              >
                Start Demo
              </Button>
              
              <Button
                size="large"
                onClick={() => window.history.back()}
                className="tw-h-12 tw-px-8 tw-text-lg"
                icon={<ArrowLeft className="tw-w-5 tw-h-5" />}
              >
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DemoPage;
